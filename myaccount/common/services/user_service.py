import random
import re
import string
import uuid
from typing import Optional, <PERSON><PERSON>
from uuid import UUI<PERSON>

from config import settings
from config.celery import app
from custom.keeps_exception_handler import KeepsBadRequestError, KeepsNotAllowedError
from django.db import IntegrityError
from rest_framework.generics import get_object_or_404
from task_names import NOTIFY_USER_BY_ROLE, RESEND_USER_INVITE
from utils.keycloak.user_manager import KeyCloakUserManager
from utils.utils import convert_possible_float_to_string

from common.models import Role, User, UserProfileWorkspace, UserRoleWorkspace, Workspace
from common.services.base_service import BaseService

# pylint: disable=unsubscriptable-object


class UserService(BaseService):
    def __init__(self):
        self.keycloak_user_client = KeyCloakUserManager()

    def _get_keycloak_user_id(self, email):
        return self.keycloak_user_client.get_user_id_by_email(email)

    def _get_user_by_id(self, user_id):
        return self.keycloak_user_client.get_user(user_id)

    def _create_keycloak_user(self, data):
        return self.keycloak_user_client.create_user(data)

    def _update_keycloak_user(self, user: User):
        locale = self._kc_format_locale(user.language.name) if user.language else "pt-BR"
        data = {"firstName": user.name, "username": user.email, "email": user.email, "attributes": {"locale": [locale]}}
        return self.keycloak_user_client.update_user(user.id, data)

    def update_user_password(self, user_id, password, temporary):
        return self.keycloak_user_client.set_user_password(user_id=user_id, password=password, temporary=temporary)

    @staticmethod
    def _kc_format_locale(locale):
        if locale is None:
            return "pt-BR"

        _locale_parts = locale.split("-")

        if len(_locale_parts) > 1:
            return f"{_locale_parts[0].lower()}-{_locale_parts[1].upper()}"

        return locale.lower()

    def get_queryset(self, user_id, **args):
        workspace_user = UserRoleWorkspace.objects.filter(
            workspace_id__in=self.get_allowed_workspaces(user_id)
        ).values_list("user", flat=True)

        return User.objects.filter(id__in=workspace_user, **args)

    def get_workspaces_queryset(self, user_logged_id, user_id):
        """
        List workspaces where user has access
        :param user_logged_id:
        :param user_id:
        :return:
        """
        workspace_user = UserRoleWorkspace.objects.filter(
            user_id=user_id, workspace_id__in=self.get_allowed_workspaces(user_logged_id)
        ).values_list("workspace", flat=True)

        return Workspace.objects.filter(id__in=workspace_user)

    def save_with_validated_data(
        self,
        data: dict,
        workspace_id: str,
        identity_provider: dict = None,
        password: str = None,
        self_register: bool = False,
        profile: dict = None,
        temporary_password: bool = True,
    ) -> Tuple[User, str]:
        if "job" in data:
            job = data.pop("job")
            if profile and "job" not in profile:
                profile["job"] = job
        if "ein" in data:
            data["ein"] = convert_possible_float_to_string(data["ein"])
        if "cpf" in data:
            data["cpf"] = data["cpf"].zfill(11)
        user = User(**data)
        older_user = User.objects.filter(email=user.email).first()
        if older_user:
            user = older_user
            user.update(**self._remove_empty_values(data))
            user.save()
        else:
            password = password if password else self.generate_random_password()
            user.id = self.get_or_create_keycloak_user(user)
            user.save()

        user_workspace_role_exists = UserRoleWorkspace.objects.filter(
            workspace_id=workspace_id, user_id=str(user.id), role__key="account_admin"
        ).exists()
        if not user_workspace_role_exists:
            self.add_default_role(workspace_id=workspace_id, user_id=user.id, self_sign_up=self_register)

        if password:
            self.update_user_password(user.id, password, temporary_password)
        if profile:
            self._save_profile(user.id, workspace_id, profile)

        self.add_identity_provider(identity_provider, user.id, user.email)
        return user, password

    def _save_profile(self, user_id: str, workspace_id: str, profile_data: dict):
        profile = UserProfileWorkspace.objects.filter(user_id=user_id, workspace_id=workspace_id).first()
        job = profile_data.pop("job", None)
        if profile:
            profile = self.update_profile(profile, profile_data)
            profile.job = job
            profile.save()
            return profile
        profile = UserProfileWorkspace(**profile_data)
        profile.user_id = user_id
        profile.workspace_id = workspace_id
        profile.job = job
        profile.save()
        return profile

    @staticmethod
    def update_profile(profile: UserProfileWorkspace, profile_data: dict):
        profile.__dict__.update(**profile_data)
        profile.job_position = profile_data.get("job_position", None)
        profile.job_function = profile_data.get("job_function", None)
        profile.save()
        return profile

    @staticmethod
    def _remove_empty_values(data: dict) -> dict:
        return {key: value for key, value in data.items() if value is not None}

    def save(
        self,
        data: dict,
        authenticate_user_id: str = None,
        workspace_id: str = None,
        identity_provider: dict = None,
        user_profile: Optional[dict] = None,
        password="keeps@123",
    ):
        self.check_log_permission(authenticate_user_id, workspace_id)
        self_register = data.pop("self_register", False) or bool(identity_provider)
        data = {k: v for k, v in data.items() if v is not None and v != ""}
        if data.get("related_user_leader"):
            leader = User.objects.filter(email=data.get("related_user_leader")).first()
            if leader:
                data["related_user_leader"] = leader
            else:
                data["related_user_leader"] = None
        user = User(**data)
        user.email = user.email.lower().strip() if user.email else None
        user.name = user.name.strip() if user.name else None

        if user.phone:
            user.phone = re.sub("[^A-Za-z0-9]+", "", user.phone)
        older_user = User.objects.filter(email=user.email).first()
        if older_user:
            user = self.update(older_user.id, data)
        else:
            user.id = self.get_or_create_keycloak_user(user)
            user.save()

        self.add_identity_provider(identity_provider, user.id, user.email)
        self.add_default_role(workspace_id=workspace_id, user_id=user.id, self_sign_up=self_register)
        if user_profile:
            self._save_profile(user.id, workspace_id, user_profile)

        return user

    def add_identity_provider(self, identity_provider: dict, user_id: UUID, email: str) -> None:
        if not identity_provider:
            return
        self.keycloak_user_client.save_identity_provider(
            user_id,
            identity_provider["alias"],
            email,
            email,
        )

    def get_or_create_keycloak_user(self, user: User) -> str:
        user_keycloak_id = self._get_keycloak_user_id(user.email)
        if user_keycloak_id:
            user.id = user_keycloak_id
            self._update_keycloak_user(user)
            return user_keycloak_id
        locale = self._kc_format_locale(user.language.name) if user.language else "pt-BR"
        payload = {
            "email": user.email,
            "username": user.email,
            "firstName": user.name,
            "enabled": True,
            "emailVerified": True,
            "attributes": {"locale": [locale]},
        }
        self._create_keycloak_user(payload)
        return self._get_keycloak_user_id(user.email)

    def check_log_permission(self, authenticate_user_id, workspace_id):
        try:
            workspaces_have_permission = list(self.get_allowed_workspaces(authenticate_user_id))
            if workspace_id not in workspaces_have_permission:
                raise KeepsNotAllowedError(
                    "Action not allowed. Do not have permission to manager this workspace account",
                    "not_permission_to_add_users",
                )
        except Exception as exc:
            raise KeepsNotAllowedError(
                "Action not allowed. Error to try authenticate your identity", "admin_identity_not_identify"
            ) from exc

    def self_sign_up(self, user, workspace_id):
        """
        create new users from application that allowed self registration

        :param user: Dict with user information
        :param workspace_id: workspace id to link the user to add
        :return: user instance
        """
        try:
            self._create_keycloak_user(
                {
                    "email": user.get("email"),
                    "username": user.get("email"),
                    "firstName": user.get("name"),
                    "enabled": True,
                    "emailVerified": True,
                    "credentials": [{"value": user["password"], "type": "password", "temporary": False}],
                }
            )
        except Exception as exc:
            raise KeepsBadRequestError(
                i18n="create_or_update_user_signup", detail=f"Error que create or update user.\n{exc}"
            ) from exc

        user_keycloak_id = self._get_keycloak_user_id(user.get("email"))
        instance = User.objects.filter(id=user_keycloak_id).first()
        permissions = user.get("permissions", None)
        user = {k: v for k, v in user.items() if v is not None and v != ""}

        if not instance:
            del user["password"]
            del user["permissions"]
            instance = User(id=user_keycloak_id, **user)
            instance.save()
        else:
            instance.__dict__.update(user)

        self.add_default_role(workspace_id=workspace_id, user_id=instance.id, self_sign_up=True)
        self.add_roles(user_id=instance.id, workspace_id=workspace_id, roles=permissions, self_signup=True)

        return instance

    def update(self, user_id: str, data: dict, workspace_id: str = None) -> User:
        users_available = User.objects.filter(is_user_integration=False)
        user = get_object_or_404(users_available, pk=user_id)
        if "ein" in data:
            data["ein"] = convert_possible_float_to_string(data["ein"])
        for attribute, value in data.items():
            setattr(user, attribute, value)
        user.save()
        if "job" in data:
            profile, _ = UserProfileWorkspace.objects.get_or_create(user_id=user_id, workspace_id=workspace_id)
            if profile:
                profile.job = data["job"]
                profile.save()
        self._update_keycloak_user(user)
        return user

    def delete(self, user_id):
        user = get_object_or_404(User, pk=user_id)
        user_keycloak = self._get_user_by_id(user_id)

        if not user_keycloak:
            return None

        user.delete()
        return self.keycloak_user_client.delete_user(user_id)

    def add_role_application(self, user_logged, data, integration=False):
        if integration:
            return self._add_role_by_integration(data)
        user_roles = UserRoleWorkspace.objects.filter(user=user_logged, workspace=str(data["workspace"].id))
        roles = list(user_roles)
        if not self.check_allowed_add_roles(roles, data["role"].id):
            raise KeepsNotAllowedError(
                detail="Not allowed to add roles for this workspace. Only Admin can do this",
                i18n="not_allowed_add_roles",
            )
        instance = UserRoleWorkspace()
        instance.role = data["role"]
        instance.user = data["user"]
        instance.self_sign_up = data.get("self_sign_up", False)
        instance.workspace = data["workspace"]
        instance.save()

        is_first_user_role = self._is_first_user_role(instance)
        password = None
        if is_first_user_role and not instance.self_sign_up:
            password = self.generate_random_password()
            self.update_user_password(instance.user_id, password, True)

        self._notify_user(instance, password)
        return instance

    def _add_role_by_integration(self, data):
        instance = UserRoleWorkspace()
        instance.role = data["role"]
        instance.user = data["user"]
        instance.workspace = data["workspace"]
        self._notify_user(instance)
        return instance

    def delete_role_application(self, user_logged, role_id, integration=False):
        if integration:
            user_role_workspace = get_object_or_404(UserRoleWorkspace, id=role_id)
            return user_role_workspace.delete()

        user_role_workspace = get_object_or_404(UserRoleWorkspace, id=role_id)
        user_roles = UserRoleWorkspace.objects.filter(user=user_logged, workspace=user_role_workspace.workspace_id)
        roles = list(user_roles)

        if self.check_allowed_add_roles(roles, str(user_role_workspace.role_id)):
            return user_role_workspace.delete()

        raise KeepsNotAllowedError(
            detail="Not allowed to remove roles for this workspace. Only Admin can do this",
            i18n="not_allowed_remove_roles",
        )

    @staticmethod
    def check_allowed_add_roles(user_roles, role_to_add_or_remove):
        role_to_add_instance = Role.objects.get(id=role_to_add_or_remove)
        roles_key = [x.role.key for x in user_roles]

        if "keeps_admin" in roles_key:
            return True
        if "company_admin" in roles_key and role_to_add_instance.key != "keeps_admin":
            return True

        return False

    @staticmethod
    def get_user_by_email(email):
        return User.objects.filter(email=email).first()

    def add_roles(self, workspace_id, user_id, roles, self_signup=False, password: string = None):
        user_roles = []
        for role in roles:
            try:
                user_role_workspace = UserRoleWorkspace()
                user_role_workspace.user_id = user_id
                user_role_workspace.role = Role.objects.get(id=role)
                user_role_workspace.workspace_id = workspace_id
                user_role_workspace.status = True
                user_role_workspace.self_sign_up = self_signup
                user_role_workspace.save()
                self._notify_user(user_role_workspace, password)
                user_roles.append(user_role_workspace)
            except IntegrityError as integrity_error:
                if "duplicate key" not in str(integrity_error):
                    raise integrity_error

        return user_roles

    @staticmethod
    def _is_first_user_role(user_role_workspace: UserRoleWorkspace) -> bool:
        my_acc_roles = Role.objects.filter(application_id=settings.MYACCOUNT_ID).values_list("id", flat=True)
        count_user_roles = (
            UserRoleWorkspace.objects.filter(user_id=user_role_workspace.user_id)
            .exclude(role_id__in=my_acc_roles)
            .exclude(role_id=settings.SMARTZAP_DEFAULT_ROLE)
            .count()
        )
        return count_user_roles == 1

    @staticmethod
    def _is_first_user_application_role(user_role_workspace: UserRoleWorkspace) -> bool:
        another_application_role_ids = Role.objects.filter(
            application_id=user_role_workspace.role.application_id
        ).values_list("id", flat=True)
        count_user_roles_in_application = UserRoleWorkspace.objects.filter(
            user_id=user_role_workspace.user_id,
            workspace_id=user_role_workspace.workspace_id,
            role_id__in=another_application_role_ids,
        ).count()
        return count_user_roles_in_application == 1

    def _notify_user(self, user_role_workspace: UserRoleWorkspace, password: string = None):
        if user_role_workspace.self_sign_up:
            return
        is_first_user_role = self._is_first_user_role(user_role_workspace)

        if is_first_user_role or self._is_first_user_application_role(user_role_workspace):
            app.send_task(
                NOTIFY_USER_BY_ROLE,
                args=(
                    user_role_workspace.id,
                    password,
                    is_first_user_role,
                ),
            )

    def send_invite(self, users_id, authenticate_user_id=None, workspace_id=None):
        """
        send invite to user with access data.
        """
        users_invite = []

        for user in users_id:
            workspace = Workspace.objects.filter(id=workspace_id).first()
            instance = UserRoleWorkspace.objects.filter(user=user, workspace=workspace).first()

            if not instance:
                users_invite.append({"user_id": user, "error": "myaccount_user_not_found", "sent": False})
                continue

            user_keycloak_id = self._get_keycloak_user_id(instance.user.email)

            if not user_keycloak_id:
                users_invite.append({"user_id": user, "error": "iam_user_not_found", "sent": False})
                continue

            if workspace_id and authenticate_user_id:
                workspaces_have_permission = list(self.get_allowed_workspaces(authenticate_user_id))
                if uuid.UUID(workspace_id) in workspaces_have_permission:
                    app.send_task(
                        name=RESEND_USER_INVITE,
                        args=(
                            workspace_id,
                            instance.user.id,
                        ),
                    )
                else:
                    users_invite.append({"user_id": user, "error": "not_allowed", "sent": False})

            users_invite.append({"user_id": user, "error": None, "sent": True})

        return users_invite

    @staticmethod
    def add_default_role(workspace_id, user_id, self_sign_up=False):
        account_admin_role = Role.objects.get(key="account_admin")
        user_role_workspace = UserRoleWorkspace.objects.filter(
            user_id=user_id, role=account_admin_role, workspace_id=workspace_id
        ).first()

        if not user_role_workspace:
            user_role_workspace = UserRoleWorkspace()
            user_role_workspace.user_id = user_id
            user_role_workspace.role = account_admin_role
            user_role_workspace.workspace_id = workspace_id
            user_role_workspace.self_sign_up = self_sign_up
            user_role_workspace.save()
        else:
            user_role_workspace.self_sign_up = self_sign_up
            user_role_workspace.save()
        return user_role_workspace

    @staticmethod
    def generate_random_password() -> str:
        return "".join(random.choice(string.digits) for x in range(6))
