import os
import re
from dataclasses import dataclass
from datetime import date, datetime
from typing import List, <PERSON>ple

import xlrd
from config.settings import DEFAULT_USER_LANGUAGE_PREFERENCE
from constants import GENDERS_PT_DICT
from custom.keeps_exception_handler import KeepsBadRequestError
from rest_framework.exceptions import ValidationError
from utils.email.clean_email import clean_email
from utils.utils import convert_possible_float_to_string

from common.models import LanguagePreference, User
from common.serializers.workspace_user_serializer import UserInputSerializer
from common.services.base_service import BaseService
from common.services.user_service import UserService

# flake8: noqa: SIM113

PLOT_TO_FIND_REAL_LINE = 2
TRUE_STRINGS = ("s", "y", "yes", "sim", "verdadeiro", "true")
EMAIL_VERIFIED = "email_verified"


@dataclass
class UserImportConfig:
    password: str
    identity_provider: dict
    user_profile: dict
    self_singup: bool
    email_verified: bool = None


class ImportUserService(BaseService):
    def __init__(self):
        self.user_service = UserService()

    def extract_relational_data(self, user: dict) -> UserImportConfig:
        password = None
        identity_provider = None
        user_profile = None
        self_singup = False
        email_verified = None

        if user.get("identity_provider"):
            self_singup = True
            identity_provider = user.get("identity_provider")
            user.pop("identity_provider")

        if user.get("password"):
            self_singup = True
            password = user.pop("password")
            password = convert_possible_float_to_string(password)

        if user.get("profile"):
            user_profile = user.get("profile")
            user.pop("profile")

        if EMAIL_VERIFIED in user:
            email_verified = user.get(EMAIL_VERIFIED)

        return UserImportConfig(
            identity_provider=identity_provider,
            user_profile=user_profile,
            password=password,
            self_singup=self_singup,
            email_verified=email_verified,
        )

    @staticmethod
    def get_language_preference_by_name(name):
        if not name:
            return str(LanguagePreference.objects.get(name=DEFAULT_USER_LANGUAGE_PREFERENCE).id)
        langs = list(LanguagePreference.objects.all())

        map_lang = {
            "português": "pt-br",
            "pt-br": "pt-br",
            "en": "en",
            "es": "es",
            "inglês": "en",
            "espanhol": "es",
            "portuguese": "pt-br",
            "english": "en",
            "spanish": "es",
            "portugués": "pt-br",
            "inglés": "en",
            "español": "es",
            "português(pt)": "pt-pt",
            "portuguese(pt)": "pt-pt",
            "portugués(pt)": "pt-pt",
        }

        return [str(lang.id) for lang in langs if lang.name == map_lang[name]][0]

    def parser_users(self, file, workspace_id):
        # pylint: disable=too-many-statements
        users = []
        if len(list(self.xls_read(file, 0))[0]) < 19:
            os.remove(file)
            raise KeepsBadRequestError(detail="Spreadsheet template don't match.", i18n="error_template_user_import")

        line_number = 0
        try:
            lines = self.xls_read(file)
            xls = xlrd.open_workbook(file)
            for line in lines:
                user = {}
                user["name"] = line[0]
                user["email"] = clean_email(line[1].lower())
                if isinstance(line[2], float):
                    line[2] = int(line[2])

                user["phone"] = self.phone_parser(line[2]) if line[2] else None
                user["language"] = self.get_language_preference_by_name(line[3]) if line[3] else None
                user["profile"] = {}
                user["address"] = line[5] if line[5] else None
                user["nickname"] = line[6] if line[6] else None
                user["secondary_email"] = line[7] if line[7] else None
                user["country"] = line[8] if line[8] else None
                user["ein"] = convert_possible_float_to_string(str(line[9])) if line[9] else None
                user["related_user_leader"] = line[10] if line[10] else None
                user["identity_provider"] = {}
                user["identity_provider"]["alias"] = line[11] if line[11] else None
                user["password"] = line[12] if line[12] else None
                user["profile"]["director"] = line[13] if line[13] else None
                user["profile"]["manager"] = line[14] if line[14] else None
                user["profile"]["area_of_activity"] = line[15] if line[15] else None
                user[EMAIL_VERIFIED] = line[16] if line[16] else None
                user["cpf"] = convert_possible_float_to_string(str(line[17])) if line[17] else None
                user["admission_date"] = (
                    datetime(*xlrd.xldate_as_tuple(line[18], xls.datemode)).strftime("%Y-%m-%d") if line[18] else None
                )
                user["ethnicity"] = line[19] if line[19] else None
                user["gender"] = (
                    GENDERS_PT_DICT[line[20].upper()] if line[20] and line[20].upper() in GENDERS_PT_DICT else None
                )
                user["marital_status"] = line[21] if line[21] else None
                user["education"] = line[22] if line[22] else None
                user["hierarchical_level"] = line[23] if line[23] else None
                user["contract_type"] = line[24] if line[24] else None
                user["birthday"] = (
                    datetime(*xlrd.xldate_as_tuple(line[25], xls.datemode)).strftime("%Y-%m-%d") if line[25] else None
                )
                user["profile"]["job"] = line[4] if line[4] else None
                user = self.remove_empty_fields(user)
                validated_data = self.validate_user_data(user, workspace_id)
                users.append(validated_data)
                line_number += 1
        except ValidationError as validation_error:
            real_line_in_file = line_number + PLOT_TO_FIND_REAL_LINE
            validation_error.detail = {str(real_line_in_file): validation_error.detail}
            raise validation_error
        except Exception as exception:
            os.remove(file)
            raise KeepsBadRequestError(
                detail="Error to parse XLS file.", i18n="error_to_parse_xls_user_import"
            ) from exception

        os.remove(file)

        return users

    def import_users(
        self, users: List[dict], roles: List[str], workspace_id: str, temporary_password: bool
    ) -> Tuple[List[User], List[dict]]:
        imported = []
        errors = []

        for user in users:
            try:
                instance = self.import_user(roles, temporary_password, user, workspace_id)
                imported.append(instance)
            except Exception as e:
                user["error"] = "Error to import user. Check if our support and send the log_error"
                user["log_error"] = str(e)
                if "language" in user:
                    user.pop("language")
                errors.append(user)

        return imported, errors

    def import_user(self, roles: List[str], temporary_password: bool, user: dict, workspace_id: str) -> User:
        user_import_config = self.extract_relational_data(user)
        self_singup = user_import_config.self_singup

        if user_import_config.email_verified is not None and EMAIL_VERIFIED not in user:
            user[EMAIL_VERIFIED] = user_import_config.email_verified

        instance, password = self.user_service.save_with_validated_data(
            data=user,
            workspace_id=workspace_id,
            identity_provider=user_import_config.identity_provider,
            password=user_import_config.password,
            profile=user_import_config.user_profile,
            self_register=user_import_config.self_singup,
            temporary_password=temporary_password,
        )
        self.user_service.add_default_role(workspace_id=workspace_id, user_id=instance.id, self_sign_up=self_singup)
        if roles:
            self.user_service.add_roles(
                workspace_id,
                instance.id,
                roles,
                self_singup,
                password,
            )
        return instance

    def parser_kafka_message(self, payload: dict) -> dict:
        user = payload.copy()
        user["ein"] = str(user.get("ein")) if "ein" in user else None
        user["profile"] = {}
        user["profile"]["director"] = user.get("director")
        user["profile"]["manager"] = user.get("manager")
        user["profile"]["area_of_activity"] = user.get("area_of_activity")
        user["gender"] = self._get_gender(user)
        user["job"] = user.get("job")
        user["hierarchical_level"] = user.get("hierarchical_level")
        user["profile"]["job_function"] = user.get("job_function")
        user["profile"]["job_position"] = user.get("job_function")

        if isinstance(user.get("birthday"), date):
            user["birthday"] = user["birthday"].strftime("%Y-%m-%d")
        if isinstance(user.get("admission_date"), date):
            user["admission_date"] = user["admission_date"].strftime("%Y-%m-%d")

        user = self.remove_empty_fields(user)
        formatted_data = self.format_boolean_fields(user)

        if "profile" in user:
            profile_fields = user["profile"].keys()
            for field in profile_fields:
                user.pop(field, None)

        return formatted_data

    @staticmethod
    def _get_gender(user):
        gender = user.get("gender")
        if not gender:
            return

        gender = gender.upper()
        return GENDERS_PT_DICT.get(gender)

    def remove_empty_fields(self, data: dict) -> dict:
        cleaned_data = {}
        for field, value in data.items():
            if isinstance(value, dict):
                value = self.remove_empty_fields(value)
            if value:
                cleaned_data.update({field: value})
        return cleaned_data

    def validate_user_data(self, data: dict, workspace_id: str) -> dict:
        formatted_data = self.format_boolean_fields(data)
        serializer = UserInputSerializer(data=formatted_data, partial=True, context={"workspace_id": workspace_id})
        serializer.is_valid(True)
        return serializer.validated_data

    @staticmethod
    def format_boolean_fields(data):
        boolean_fields = [EMAIL_VERIFIED, "status"]
        for field in boolean_fields:
            if field in data and data.get(field) is not None:
                value = str(data.get(field)).strip().lower()
                value = value in TRUE_STRINGS
                data[field] = value
        return data

    @staticmethod
    def xls_read(arq_xls, start_line=1):
        """
        read
        """

        xls = xlrd.open_workbook(arq_xls)
        plan = xls.sheets()[0]
        for i in range(start_line, plan.nrows):
            yield plan.row_values(i)

    @staticmethod
    def phone_parser(phone):
        return "{}".format(re.sub(r"[(,), " ",.,-]", "", str(phone)))
